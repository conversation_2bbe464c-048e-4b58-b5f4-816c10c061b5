import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

sns.set_style("whitegrid")


def data_analysis_plot(j):
    # 读取原始训练集数据
    raw1 = np.loadtxt("train_FD00" + str(j) + ".txt")
    df1 = pd.DataFrame(raw1, columns=['unit', 'cycles', 'operational setting 1', 'operational setting 2',
                                      'operational setting 3'] + ['sensor measurement' + str(i) for i in
                                                                  range(1, 22)])
    # print(df1.iloc[:, 5:].describe())

    # 绘制传感器读数变化曲线
    plt.figure(figsize=(20, 12))  # 增大图形尺寸
    plt.suptitle('Dataset ' + str(j) + ' Sensor Measurements Over Time', fontsize=16, y=0.98)

    for i in range(1, 22):
        plt.subplot(5, 5, i)
        plt.title(f'Sensor {i}', fontsize=10)
        df1.iloc[:, i + 4].plot()
        plt.xlabel('Index', fontsize=8)
        plt.ylabel('Value', fontsize=8)
        plt.xticks(fontsize=7)
        plt.yticks(fontsize=7)

    plt.subplots_adjust(hspace=0.6, wspace=0.4, top=0.9)
    plt.show()

    # 绘制传感器数据分布直方图
    plt.figure(figsize=(20, 12))  # 增大图形尺寸
    plt.suptitle('Dataset ' + str(j) + ' Sensor Measurements Distribution', fontsize=16, y=0.98)

    for i in range(1, 22):
        plt.subplot(5, 5, i)
        plt.title(f'Sensor {i}', fontsize=10)
        sns.distplot(df1.iloc[:, i + 4])
        plt.xlabel('Value', fontsize=8)
        plt.ylabel('Density', fontsize=8)
        plt.xticks(fontsize=7)
        plt.yticks(fontsize=7)

    plt.subplots_adjust(hspace=0.6, wspace=0.4, top=0.9)
    plt.show()


if __name__ == '__main__':
    data_analysis_plot(1)
    data_analysis_plot(2)
    data_analysis_plot(3)
    data_analysis_plot(4)
